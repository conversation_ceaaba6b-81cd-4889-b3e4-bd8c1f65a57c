import os
import subprocess
import time
import signal
import psutil
import threading
from pathlib import Path
from ..utils import logger


class MilvusManager:
    """Milvus服务器管理器"""

    def __init__(self, data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
        self.data_dir = Path(data_dir).resolve()
        self.port = port
        self.host = host
        self.process = None
        self.is_running = False

        # 确保数据目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)

    def _check_milvus_installed(self):
        """检查pymilvus是否已安装"""
        try:
            import pymilvus
            return True
        except ImportError:
            return False

    def _install_milvus(self):
        """安装pymilvus"""
        logger.info("正在安装pymilvus...")
        try:
            subprocess.run(['pip', 'install', 'pymilvus'], check=True)
            logger.info("pymilvus安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"pymilvus安装失败: {e}")
            return False

    def _is_port_in_use(self, port):
        """检查端口是否被占用"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return True
        return False

    def _get_process_using_port(self, port):
        """获取占用指定端口的进程ID"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.pid:
                return conn.pid
        return None

    def _kill_process_using_port(self, port):
        """杀死占用指定端口的进程"""
        pid = self._get_process_using_port(port)
        if pid:
            try:
                process = psutil.Process(pid)
                process_name = process.name()
                logger.info(f"正在关闭占用端口 {port} 的进程: {process_name} (PID: {pid})")

                # 先尝试优雅关闭
                process.terminate()

                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    logger.info(f"进程 {process_name} (PID: {pid}) 已优雅关闭")
                    return True
                except psutil.TimeoutExpired:
                    # 强制杀死进程
                    process.kill()
                    process.wait(timeout=5)
                    logger.info(f"进程 {process_name} (PID: {pid}) 已强制关闭")
                    return True

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                logger.warning(f"无法关闭进程 {pid}: {e}")
                return False
        return False

    def _wait_for_milvus_ready(self, timeout=60):
        """等待milvus服务器就绪"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._is_port_in_use(self.port):
                # 尝试连接测试
                try:
                    from pymilvus import MilvusClient
                    client = MilvusClient(uri=f"http://{self.host}:{self.port}")
                    client.list_collections()
                    logger.info(f"Milvus服务器已就绪，监听端口 {self.port}")
                    return True
                except Exception:
                    pass
            time.sleep(1)
        return False

    def start(self):
        """启动milvus服务器（使用Milvus Lite）"""
        if self.is_running:
            logger.info("Milvus服务器已在运行")
            return True

        # 检查是否已安装pymilvus
        if not self._check_milvus_installed():
            logger.info("未检测到pymilvus，正在安装...")
            if not self._install_milvus():
                logger.error("无法安装pymilvus，请手动安装: pip install pymilvus")
                return False

        # 检查端口是否被占用
        if self._is_port_in_use(self.port):
            logger.info(f"端口 {self.port} 已被占用，假设milvus已在运行")
            if self._wait_for_milvus_ready(timeout=5):
                self.is_running = True

                # 自动加载所有现有的集合
                logger.info("正在加载现有的集合...")
                self.load_all_collections()

                return True
            else:
                logger.error(f"端口 {self.port} 被占用但无法连接到milvus服务")
                return False

        try:
            logger.info(f"正在启动milvus服务器，数据目录: {self.data_dir}")

            # 使用 Milvus Lite 启动服务器
            from milvus import default_server
            default_server.set_base_dir(str(self.data_dir))
            default_server.start()

            # 等待服务器启动
            if self._wait_for_milvus_ready():
                self.is_running = True
                logger.info("Milvus服务器启动成功")

                # 自动加载所有现有的集合
                logger.info("正在加载现有的集合...")
                self.load_all_collections()

                return True
            else:
                logger.error("Milvus服务器启动超时")
                self.stop()
                return False

        except Exception as e:
            logger.error(f"启动milvus服务器失败: {e}")
            return False

    def _start_log_monitor(self):
        """启动日志监控线程"""
        def monitor_logs():
            if self.process:
                for line in iter(self.process.stdout.readline, ''):
                    if line.strip():
                        logger.debug(f"Milvus: {line.strip()}")
                    if not self.is_running:
                        break

        log_thread = threading.Thread(target=monitor_logs, daemon=True)
        log_thread.start()

    def stop(self, force_kill_port=True):
        """停止milvus服务器

        Args:
            force_kill_port (bool): 是否强制关闭占用端口的进程
        """
        self.is_running = False

        # 首先尝试优雅关闭 Milvus Lite 服务器
        try:
            from milvus import default_server
            if default_server.running:
                default_server.stop()
                logger.info("Milvus Lite服务器已停止")
        except Exception as e:
            logger.warning(f"停止Milvus Lite服务器时出错: {e}")

        # 如果还有自己启动的进程，也要关闭
        if self.process:
            try:
                # 优雅关闭
                self.process.terminate()

                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                    logger.info("Milvus进程已停止")
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.process.kill()
                    self.process.wait()
                    logger.info("Milvus进程已强制停止")

            except Exception as e:
                logger.error(f"停止milvus进程时出错: {e}")
            finally:
                self.process = None

        # 如果端口仍被占用且启用了强制关闭，则关闭占用端口的进程
        if force_kill_port and self._is_port_in_use(self.port):
            logger.info(f"端口 {self.port} 仍被占用，尝试强制关闭占用进程...")
            if self._kill_process_using_port(self.port):
                logger.info(f"已成功关闭占用端口 {self.port} 的进程")
            else:
                logger.warning(f"无法关闭占用端口 {self.port} 的进程")

        # 验证端口是否已释放
        if not self._is_port_in_use(self.port):
            logger.info(f"端口 {self.port} 已释放")
        else:
            logger.warning(f"端口 {self.port} 仍被占用")

    def restart(self):
        """重启milvus服务器"""
        logger.info("正在重启milvus服务器...")
        self.stop()
        time.sleep(2)
        return self.start()

    def load_all_collections(self):
        """加载所有现有的集合到内存中"""
        try:
            from pymilvus import MilvusClient
            client = MilvusClient(uri=f"http://{self.host}:{self.port}")
            collections = client.list_collections()

            loaded_count = 0
            for collection_name in collections:
                try:
                    client.load_collection(collection_name=collection_name)
                    logger.info(f"Collection {collection_name} loaded successfully")
                    loaded_count += 1
                except Exception as e:
                    logger.warning(f"Failed to load collection {collection_name}: {e}")

            logger.info(f"Loaded {loaded_count} out of {len(collections)} collections")
            return loaded_count

        except Exception as e:
            logger.error(f"Failed to load collections: {e}")
            return 0

    def get_status(self):
        """获取milvus服务器状态"""
        if not self.is_running:
            return {"status": "stopped", "port": self.port, "data_dir": str(self.data_dir)}

        try:
            from pymilvus import MilvusClient
            client = MilvusClient(uri=f"http://{self.host}:{self.port}")
            collections = client.list_collections()
            return {
                "status": "running",
                "port": self.port,
                "host": self.host,
                "data_dir": str(self.data_dir),
                "collections_count": len(collections)
            }
        except Exception as e:
            return {
                "status": "error",
                "port": self.port,
                "data_dir": str(self.data_dir),
                "error": str(e)
            }


# 全局milvus管理器实例
milvus_manager = None

def get_milvus_manager(data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
    """获取milvus管理器实例"""
    global milvus_manager
    if milvus_manager is None:
        milvus_manager = MilvusManager(data_dir, port, host)
    return milvus_manager

def start_milvus_server(data_dir="./milvus_lite", port=19530, host="127.0.0.1"):
    """启动milvus服务器的便捷函数"""
    manager = get_milvus_manager(data_dir, port, host)
    return manager.start()

def stop_milvus_server(force_kill_port=True):
    """停止milvus服务器的便捷函数"""
    global milvus_manager
    if milvus_manager:
        milvus_manager.stop(force_kill_port=force_kill_port)

def force_kill_milvus_port(port=19530):
    """强制关闭占用Milvus端口的进程"""
    manager = MilvusManager(port=port)
    if manager._is_port_in_use(port):
        logger.info(f"正在强制关闭占用端口 {port} 的进程...")
        if manager._kill_process_using_port(port):
            logger.info(f"已成功关闭占用端口 {port} 的进程")
            return True
        else:
            logger.warning(f"无法关闭占用端口 {port} 的进程")
            return False
    else:
        logger.info(f"端口 {port} 未被占用")
        return True
