
# ThreatRAG 统一配置文件

# 功能开关
enable_reranker: true
enable_knowledge_base: true
enable_knowledge_graph: true
enable_web_search: false

# 查询配置
use_rewrite_query: "on"  # 是否使用重写查询

# 模型配置
model_provider: "deepseek"
model_name: "deepseek-chat"

# 嵌入模型
embed_model: "local/BAAI/bge-m3"

# Reranker 模型 - 使用相同的bge-m3模型
reranker: "local/BAAI/bge-m3"
model_local_paths:
  local/BAAI/bge-m3: "models/embedding_model/bge-m3"

# 设备配置
device: "cuda"

# FastAPI服务器配置
fastapi_server:
  host: localhost
  port: 8000

# 向量数据库配置
vector_database:
  path: /rag/data/vector_db

# 文件上传配置
file_upload:
  path: /rag/data/file_uploads

# 模型路径配置
model:
  embedding_model:
    path: /rag/data/embedding_model

# Milvus配置
milvus:
  auto_start: false
  data_dir: ./milvus_lite
  host: 127.0.0.1
  port: 19530

# Neo4j配置
neo4j:
  auto_start: false
  data_dir: ./neo4j_data
  host: 127.0.0.1
  port: 7687
  http_port: 7474
  # 添加索引器配置
  index_interval: 600  # 索引间隔，单位为秒，默认1小时
  index_batch_size: 200  # 每次处理的实体数量
  auto_index: true  # 是否自动启动索引器

# PostgreSQL配置
postgres:
  host: localhost
  port: 5432
  user: postgres
  password: 12345678
  database: knowledge_db
